// SAM - Material Consumption Management System
// JavaScript Functions

class MaterialManager {
    constructor() {
        this.materials = this.loadMaterials();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateDashboard();
        this.showSection('dashboard');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('[data-section]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('data-section');
                this.showSection(section);
                this.updateNavigation(e.target);
            });
        });

        // Material Form
        const materialForm = document.getElementById('materialForm');
        if (materialForm) {
            materialForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addMaterial();
            });
        }

        // Search Form
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Clear Search
        document.getElementById('clearSearchBtn')?.addEventListener('click', () => {
            this.clearSearch();
        });

        // Report Forms
        document.getElementById('reportByDateForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateReportByDate();
        });

        document.getElementById('comprehensiveReportForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateComprehensiveReport();
        });

        document.getElementById('dailyReportForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateDailyReport();
        });

        // Quick Search
        document.getElementById('quickSearch')?.addEventListener('input', (e) => {
            this.quickSearch(e.target.value);
        });

        // Sort buttons
        document.getElementById('sortByDate')?.addEventListener('click', () => {
            this.sortMaterials('date');
        });

        document.getElementById('sortByType')?.addEventListener('click', () => {
            this.sortMaterials('type');
        });

        document.getElementById('sortByMeters')?.addEventListener('click', () => {
            this.sortMaterials('meters');
        });

        // Export/Import buttons
        document.getElementById('exportDataBtn')?.addEventListener('click', () => {
            this.exportData();
        });

        document.getElementById('importDataBtn')?.addEventListener('click', () => {
            this.importData();
        });

        // Report actions
        document.getElementById('printReportBtn')?.addEventListener('click', () => {
            this.printReport();
        });

        document.getElementById('downloadReportBtn')?.addEventListener('click', () => {
            this.downloadReport();
        });
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('d-none');
        });

        // Show selected section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.remove('d-none');
        }

        // Update content based on section
        if (sectionName === 'dashboard') {
            this.updateDashboard();
        } else if (sectionName === 'materials-list') {
            this.updateMaterialsList();
        }
    }

    updateNavigation(activeLink) {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to clicked link
        activeLink.classList.add('active');
    }

    addMaterial() {
        const formData = {
            id: Date.now().toString(),
            startDate: document.getElementById('startDate').value,
            endDate: document.getElementById('endDate').value,
            materialType: document.getElementById('materialType').value,
            materialColor: document.getElementById('materialColor').value,
            modelNumber: document.getElementById('modelNumber').value,
            metersUsed: parseFloat(document.getElementById('metersUsed').value),
            producedCount: parseInt(document.getElementById('producedCount').value),
            deliveredCount: parseInt(document.getElementById('deliveredCount').value),
            materialStatus: document.getElementById('materialStatus').value,
            notes: document.getElementById('notes').value,
            reportDate: new Date().toISOString().split('T')[0]
        };

        // Validation
        if (!this.validateMaterial(formData)) {
            return;
        }

        // Add to materials array
        this.materials.push(formData);
        
        // Save to localStorage
        this.saveMaterials();
        
        // Show success message
        this.showAlert('تم إضافة المادة بنجاح!', 'success');
        
        // Reset form
        document.getElementById('materialForm').reset();
        
        // Update dashboard
        this.updateDashboard();
    }

    validateMaterial(material) {
        // Check if delivered count is not greater than produced count
        if (material.deliveredCount > material.producedCount) {
            this.showAlert('العدد المسلم لا يمكن أن يكون أكبر من العدد المنتج!', 'danger');
            return false;
        }

        // Check if end date is after start date
        if (new Date(material.endDate) < new Date(material.startDate)) {
            this.showAlert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية!', 'danger');
            return false;
        }

        // Check if meters used is positive
        if (material.metersUsed <= 0) {
            this.showAlert('الأمتار المستخدمة يجب أن تكون أكبر من صفر!', 'danger');
            return false;
        }

        return true;
    }

    updateDashboard() {
        // Calculate statistics
        const stats = this.calculateStatistics();
        
        // Update statistics cards
        document.getElementById('totalMaterials').textContent = stats.totalMaterials;
        document.getElementById('totalMeters').textContent = stats.totalMeters.toFixed(2);
        document.getElementById('totalProduced').textContent = stats.totalProduced;
        document.getElementById('totalDelivered').textContent = stats.totalDelivered;
        
        // Update recent materials
        this.updateRecentMaterials();
    }

    calculateStatistics() {
        return {
            totalMaterials: this.materials.length,
            totalMeters: this.materials.reduce((sum, material) => sum + material.metersUsed, 0),
            totalProduced: this.materials.reduce((sum, material) => sum + material.producedCount, 0),
            totalDelivered: this.materials.reduce((sum, material) => sum + material.deliveredCount, 0)
        };
    }

    updateRecentMaterials() {
        const recentContainer = document.getElementById('recentMaterials');
        
        if (this.materials.length === 0) {
            recentContainer.innerHTML = '<p class="text-muted text-center py-3">لا توجد مواد مضافة بعد</p>';
            return;
        }

        // Get last 5 materials
        const recentMaterials = this.materials
            .sort((a, b) => new Date(b.reportDate) - new Date(a.reportDate))
            .slice(0, 5);

        const tableHTML = `
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>نوع المادة</th>
                        <th>اللون</th>
                        <th>الموديل</th>
                        <th>الأمتار</th>
                        <th>المنتج</th>
                        <th>المسلم</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    ${recentMaterials.map(material => `
                        <tr>
                            <td>${material.materialType}</td>
                            <td>${material.materialColor}</td>
                            <td>${material.modelNumber}</td>
                            <td>${material.metersUsed}</td>
                            <td>${material.producedCount}</td>
                            <td>${material.deliveredCount}</td>
                            <td>${this.formatDate(material.reportDate)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        recentContainer.innerHTML = tableHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    showAlert(message, type = 'info') {
        // Remove existing alerts
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        // Create new alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at the top of main content
        const main = document.querySelector('main');
        main.insertBefore(alert, main.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    loadMaterials() {
        const stored = localStorage.getItem('sam_materials');
        return stored ? JSON.parse(stored) : [];
    }

    saveMaterials() {
        localStorage.setItem('sam_materials', JSON.stringify(this.materials));
    }

    exportData() {
        if (this.materials.length === 0) {
            this.showAlert('لا توجد بيانات للتصدير!', 'warning');
            return;
        }

        const dataStr = JSON.stringify(this.materials, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `sam_materials_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        this.showAlert('تم تصدير البيانات بنجاح!', 'success');
    }

    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (Array.isArray(importedData)) {
                        this.materials = importedData;
                        this.saveMaterials();
                        this.updateDashboard();
                        this.showAlert('تم استيراد البيانات بنجاح!', 'success');
                    } else {
                        this.showAlert('ملف البيانات غير صحيح!', 'danger');
                    }
                } catch (error) {
                    this.showAlert('خطأ في قراءة الملف!', 'danger');
                }
            };
            reader.readAsText(file);
        };

        input.click();
    }

    // Search Functions
    performSearch() {
        const filters = {
            materialType: document.getElementById('searchMaterialType').value,
            color: document.getElementById('searchColor').value.toLowerCase(),
            model: document.getElementById('searchModel').value.toLowerCase(),
            status: document.getElementById('searchStatus').value,
            startDate: document.getElementById('searchStartDate').value,
            endDate: document.getElementById('searchEndDate').value
        };

        const filteredMaterials = this.materials.filter(material => {
            // Filter by material type
            if (filters.materialType && material.materialType !== filters.materialType) {
                return false;
            }

            // Filter by color
            if (filters.color && !material.materialColor.toLowerCase().includes(filters.color)) {
                return false;
            }

            // Filter by model
            if (filters.model && !material.modelNumber.toLowerCase().includes(filters.model)) {
                return false;
            }

            // Filter by status
            if (filters.status && material.materialStatus !== filters.status) {
                return false;
            }

            // Filter by date range
            if (filters.startDate && material.startDate < filters.startDate) {
                return false;
            }

            if (filters.endDate && material.endDate > filters.endDate) {
                return false;
            }

            return true;
        });

        this.displaySearchResults(filteredMaterials);
    }

    displaySearchResults(materials) {
        const resultsContainer = document.getElementById('searchResults');
        const countElement = document.getElementById('searchResultsCount');
        const statsCard = document.getElementById('searchStatsCard');

        countElement.textContent = `${materials.length} نتيجة`;

        if (materials.length === 0) {
            resultsContainer.innerHTML = `
                <p class="text-muted text-center py-4">
                    <i class="bi bi-search fs-1 d-block mb-2"></i>
                    لم يتم العثور على نتائج مطابقة للبحث
                </p>
            `;
            statsCard.style.display = 'none';
            return;
        }

        // Display results table
        const tableHTML = `
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>نوع المادة</th>
                        <th>اللون</th>
                        <th>الموديل</th>
                        <th>الأمتار</th>
                        <th>المنتج</th>
                        <th>المسلم</th>
                        <th>الحالة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                    </tr>
                </thead>
                <tbody>
                    ${materials.map(material => `
                        <tr>
                            <td><span class="badge bg-primary">${material.materialType}</span></td>
                            <td>${material.materialColor}</td>
                            <td>${material.modelNumber}</td>
                            <td>${material.metersUsed}</td>
                            <td>${material.producedCount}</td>
                            <td>${material.deliveredCount}</td>
                            <td>
                                <span class="badge ${this.getStatusBadgeClass(material.materialStatus)}">
                                    ${material.materialStatus}
                                </span>
                            </td>
                            <td>${this.formatDate(material.startDate)}</td>
                            <td>${this.formatDate(material.endDate)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        resultsContainer.innerHTML = tableHTML;

        // Update search statistics
        this.updateSearchStatistics(materials);
        statsCard.style.display = 'block';
    }

    updateSearchStatistics(materials) {
        const stats = {
            totalMaterials: materials.length,
            totalMeters: materials.reduce((sum, material) => sum + material.metersUsed, 0),
            totalProduced: materials.reduce((sum, material) => sum + material.producedCount, 0),
            totalDelivered: materials.reduce((sum, material) => sum + material.deliveredCount, 0)
        };

        document.getElementById('searchTotalMaterials').textContent = stats.totalMaterials;
        document.getElementById('searchTotalMeters').textContent = stats.totalMeters.toFixed(2);
        document.getElementById('searchTotalProduced').textContent = stats.totalProduced;
        document.getElementById('searchTotalDelivered').textContent = stats.totalDelivered;
    }

    clearSearch() {
        document.getElementById('searchForm').reset();
        document.getElementById('searchResults').innerHTML = `
            <p class="text-muted text-center py-4">
                <i class="bi bi-search fs-1 d-block mb-2"></i>
                استخدم الفلاتر للبحث عن المواد
            </p>
        `;
        document.getElementById('searchResultsCount').textContent = '0 نتيجة';
        document.getElementById('searchStatsCard').style.display = 'none';
    }

    getStatusBadgeClass(status) {
        switch (status) {
            case 'مستخدمة': return 'bg-success';
            case 'متوقفة': return 'bg-danger';
            case 'تحت الفحص': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }
}

    // Report Generation Functions
    generateReportByDate() {
        const materialType = document.getElementById('reportMaterialType').value;
        const startDate = document.getElementById('reportStartDate').value;
        const endDate = document.getElementById('reportEndDate').value;

        const filteredMaterials = this.materials.filter(material => {
            return material.materialType === materialType &&
                   material.startDate >= startDate &&
                   material.endDate <= endDate;
        });

        this.generateReport('تقرير استهلاك المواد حسب التاريخ', filteredMaterials, {
            materialType,
            startDate,
            endDate
        });
    }

    generateComprehensiveReport() {
        const materialType = document.getElementById('comprehensiveMaterialType').value;
        const color = document.getElementById('comprehensiveColor').value;
        const model = document.getElementById('comprehensiveModel').value;

        const filteredMaterials = this.materials.filter(material => {
            let matches = material.materialType === materialType;

            if (color) {
                matches = matches && material.materialColor.toLowerCase().includes(color.toLowerCase());
            }

            if (model) {
                matches = matches && material.modelNumber.toLowerCase().includes(model.toLowerCase());
            }

            return matches;
        });

        this.generateReport('التقرير الشامل للمادة', filteredMaterials, {
            materialType,
            color,
            model
        });
    }

    generateDailyReport() {
        const reportDate = document.getElementById('dailyReportDate').value;
        const materialType = document.getElementById('dailyMaterialType').value;

        const filteredMaterials = this.materials.filter(material => {
            let matches = material.reportDate === reportDate;

            if (materialType) {
                matches = matches && material.materialType === materialType;
            }

            return matches;
        });

        this.generateReport('التقرير اليومي', filteredMaterials, {
            reportDate,
            materialType
        });
    }

    generateReport(title, materials, filters) {
        const reportContent = document.getElementById('reportContent');
        const reportPreview = document.getElementById('reportPreviewArea');

        if (materials.length === 0) {
            this.showAlert('لا توجد بيانات لإنشاء التقرير!', 'warning');
            return;
        }

        const stats = {
            totalMaterials: materials.length,
            totalMeters: materials.reduce((sum, material) => sum + material.metersUsed, 0),
            totalProduced: materials.reduce((sum, material) => sum + material.producedCount, 0),
            totalDelivered: materials.reduce((sum, material) => sum + material.deliveredCount, 0)
        };

        const reportHTML = `
            <div class="report-header text-center mb-4">
                <h3>${title}</h3>
                <p class="text-muted">تاريخ إنشاء التقرير: ${this.formatDate(new Date().toISOString().split('T')[0])}</p>
                ${this.generateFiltersSummary(filters)}
            </div>

            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${stats.totalMaterials}</h4>
                            <p class="mb-0">إجمالي المواد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${stats.totalMeters.toFixed(2)}</h4>
                            <p class="mb-0">إجمالي الأمتار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${stats.totalProduced}</h4>
                            <p class="mb-0">إجمالي المنتج</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${stats.totalDelivered}</h4>
                            <p class="mb-0">إجمالي المسلم</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>نوع المادة</th>
                            <th>اللون</th>
                            <th>الموديل</th>
                            <th>الأمتار المستخدمة</th>
                            <th>العدد المنتج</th>
                            <th>العدد المسلم</th>
                            <th>الحالة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${materials.map(material => `
                            <tr>
                                <td>${material.materialType}</td>
                                <td>${material.materialColor}</td>
                                <td>${material.modelNumber}</td>
                                <td>${material.metersUsed}</td>
                                <td>${material.producedCount}</td>
                                <td>${material.deliveredCount}</td>
                                <td>${material.materialStatus}</td>
                                <td>${this.formatDate(material.startDate)}</td>
                                <td>${this.formatDate(material.endDate)}</td>
                                <td>${material.notes || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="report-footer mt-4 text-center">
                <hr>
                <p class="text-muted">
                    SAM - برنامج استهلاك المواد | المطور: MOHANNAD AHMAD | هاتف: +963998171954
                </p>
            </div>
        `;

        reportContent.innerHTML = reportHTML;
        reportPreview.style.display = 'block';

        // Store current report for download/print
        this.currentReport = {
            title,
            content: reportHTML,
            materials,
            stats
        };

        this.showAlert('تم إنشاء التقرير بنجاح!', 'success');
    }

    generateFiltersSummary(filters) {
        const filterItems = [];

        if (filters.materialType) filterItems.push(`نوع المادة: ${filters.materialType}`);
        if (filters.color) filterItems.push(`اللون: ${filters.color}`);
        if (filters.model) filterItems.push(`الموديل: ${filters.model}`);
        if (filters.startDate) filterItems.push(`من تاريخ: ${this.formatDate(filters.startDate)}`);
        if (filters.endDate) filterItems.push(`إلى تاريخ: ${this.formatDate(filters.endDate)}`);
        if (filters.reportDate) filterItems.push(`التاريخ: ${this.formatDate(filters.reportDate)}`);

        if (filterItems.length === 0) return '';

        return `<div class="alert alert-info"><strong>فلاتر التقرير:</strong> ${filterItems.join(' | ')}</div>`;
    }

    printReport() {
        if (!this.currentReport) {
            this.showAlert('لا يوجد تقرير للطباعة!', 'warning');
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${this.currentReport.title}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <style>
                    body { font-family: 'Cairo', sans-serif; }
                    @media print { .no-print { display: none !important; } }
                </style>
            </head>
            <body>
                <div class="container">
                    ${this.currentReport.content}
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    downloadReport() {
        if (!this.currentReport) {
            this.showAlert('لا يوجد تقرير للتحميل!', 'warning');
            return;
        }

        // Create Excel-like CSV content
        let csvContent = '\uFEFF'; // BOM for UTF-8
        csvContent += `${this.currentReport.title}\n`;
        csvContent += `تاريخ إنشاء التقرير,${new Date().toLocaleDateString('ar-SA')}\n\n`;

        // Headers
        csvContent += 'نوع المادة,اللون,الموديل,الأمتار المستخدمة,العدد المنتج,العدد المسلم,الحالة,تاريخ البداية,تاريخ النهاية,ملاحظات\n';

        // Data
        this.currentReport.materials.forEach(material => {
            csvContent += `${material.materialType},${material.materialColor},${material.modelNumber},${material.metersUsed},${material.producedCount},${material.deliveredCount},${material.materialStatus},${material.startDate},${material.endDate},"${material.notes || ''}"\n`;
        });

        // Statistics
        csvContent += '\n\nالإحصائيات\n';
        csvContent += `إجمالي المواد,${this.currentReport.stats.totalMaterials}\n`;
        csvContent += `إجمالي الأمتار,${this.currentReport.stats.totalMeters.toFixed(2)}\n`;
        csvContent += `إجمالي المنتج,${this.currentReport.stats.totalProduced}\n`;
        csvContent += `إجمالي المسلم,${this.currentReport.stats.totalDelivered}\n`;

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${this.currentReport.title}_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showAlert('تم تحميل التقرير بنجاح!', 'success');
    }

    // Materials List Functions
    updateMaterialsList() {
        const tableContainer = document.getElementById('materialsTable');

        if (this.materials.length === 0) {
            tableContainer.innerHTML = `
                <p class="text-muted text-center py-4">
                    <i class="bi bi-box-seam fs-1 d-block mb-2"></i>
                    لا توجد مواد مضافة بعد
                </p>
            `;
            return;
        }

        const tableHTML = `
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>نوع المادة</th>
                        <th>اللون</th>
                        <th>الموديل</th>
                        <th>الأمتار</th>
                        <th>المنتج</th>
                        <th>المسلم</th>
                        <th>الحالة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.materials.map(material => `
                        <tr>
                            <td><span class="badge bg-primary">${material.materialType}</span></td>
                            <td>${material.materialColor}</td>
                            <td>${material.modelNumber}</td>
                            <td>${material.metersUsed}</td>
                            <td>${material.producedCount}</td>
                            <td>${material.deliveredCount}</td>
                            <td>
                                <span class="badge ${this.getStatusBadgeClass(material.materialStatus)}">
                                    ${material.materialStatus}
                                </span>
                            </td>
                            <td>${this.formatDate(material.startDate)}</td>
                            <td>${this.formatDate(material.endDate)}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="materialManager.viewMaterial('${material.id}')" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="materialManager.deleteMaterial('${material.id}')" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        tableContainer.innerHTML = tableHTML;
    }

    quickSearch(searchTerm) {
        if (!searchTerm.trim()) {
            this.updateMaterialsList();
            return;
        }

        const filteredMaterials = this.materials.filter(material => {
            const searchLower = searchTerm.toLowerCase();
            return (
                material.materialType.toLowerCase().includes(searchLower) ||
                material.materialColor.toLowerCase().includes(searchLower) ||
                material.modelNumber.toLowerCase().includes(searchLower) ||
                material.materialStatus.toLowerCase().includes(searchLower) ||
                material.notes.toLowerCase().includes(searchLower)
            );
        });

        this.displayFilteredMaterialsList(filteredMaterials);
    }

    displayFilteredMaterialsList(materials) {
        const tableContainer = document.getElementById('materialsTable');

        if (materials.length === 0) {
            tableContainer.innerHTML = `
                <p class="text-muted text-center py-4">
                    <i class="bi bi-search fs-1 d-block mb-2"></i>
                    لم يتم العثور على نتائج مطابقة للبحث
                </p>
            `;
            return;
        }

        const tableHTML = `
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>نوع المادة</th>
                        <th>اللون</th>
                        <th>الموديل</th>
                        <th>الأمتار</th>
                        <th>المنتج</th>
                        <th>المسلم</th>
                        <th>الحالة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${materials.map(material => `
                        <tr>
                            <td><span class="badge bg-primary">${material.materialType}</span></td>
                            <td>${material.materialColor}</td>
                            <td>${material.modelNumber}</td>
                            <td>${material.metersUsed}</td>
                            <td>${material.producedCount}</td>
                            <td>${material.deliveredCount}</td>
                            <td>
                                <span class="badge ${this.getStatusBadgeClass(material.materialStatus)}">
                                    ${material.materialStatus}
                                </span>
                            </td>
                            <td>${this.formatDate(material.startDate)}</td>
                            <td>${this.formatDate(material.endDate)}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="materialManager.viewMaterial('${material.id}')" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="materialManager.deleteMaterial('${material.id}')" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        tableContainer.innerHTML = tableHTML;
    }

    sortMaterials(sortBy) {
        let sortedMaterials = [...this.materials];

        switch (sortBy) {
            case 'date':
                sortedMaterials.sort((a, b) => new Date(b.reportDate) - new Date(a.reportDate));
                break;
            case 'type':
                sortedMaterials.sort((a, b) => a.materialType.localeCompare(b.materialType, 'ar'));
                break;
            case 'meters':
                sortedMaterials.sort((a, b) => b.metersUsed - a.metersUsed);
                break;
        }

        this.materials = sortedMaterials;
        this.updateMaterialsList();
        this.saveMaterials();
    }

    viewMaterial(materialId) {
        const material = this.materials.find(m => m.id === materialId);
        if (!material) return;

        const modalHTML = `
            <div class="modal fade" id="materialModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-eye"></i>
                                تفاصيل المادة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr><th>نوع المادة:</th><td>${material.materialType}</td></tr>
                                        <tr><th>اللون:</th><td>${material.materialColor}</td></tr>
                                        <tr><th>الموديل:</th><td>${material.modelNumber}</td></tr>
                                        <tr><th>الأمتار المستخدمة:</th><td>${material.metersUsed}</td></tr>
                                        <tr><th>العدد المنتج:</th><td>${material.producedCount}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr><th>العدد المسلم:</th><td>${material.deliveredCount}</td></tr>
                                        <tr><th>الحالة:</th><td><span class="badge ${this.getStatusBadgeClass(material.materialStatus)}">${material.materialStatus}</span></td></tr>
                                        <tr><th>تاريخ البداية:</th><td>${this.formatDate(material.startDate)}</td></tr>
                                        <tr><th>تاريخ النهاية:</th><td>${this.formatDate(material.endDate)}</td></tr>
                                        <tr><th>تاريخ التقرير:</th><td>${this.formatDate(material.reportDate)}</td></tr>
                                    </table>
                                </div>
                            </div>
                            ${material.notes ? `
                                <div class="mt-3">
                                    <h6>الملاحظات:</h6>
                                    <div class="alert alert-info">${material.notes}</div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('materialModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('materialModal'));
        modal.show();
    }

    deleteMaterial(materialId) {
        if (!confirm('هل أنت متأكد من حذف هذه المادة؟')) {
            return;
        }

        this.materials = this.materials.filter(m => m.id !== materialId);
        this.saveMaterials();
        this.updateMaterialsList();
        this.updateDashboard();
        this.showAlert('تم حذف المادة بنجاح!', 'success');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.materialManager = new MaterialManager();
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MaterialManager;
}
