# SAM - برنامج استهلاك المواد

## 🎯 نظرة عامة

**SAM (System for Advanced Materials)** هو برنامج شامل ومتكامل لإدارة وتتبع استهلاك المواد في عمليات الإنتاج. مصمم بدقة ليلبي احتياجات المنشآت الصناعية والخدمية التي تعتمد على تتبع المواد من حيث الكمية والتاريخ والنوع والجودة.

**المطور:** MOHANNAD AHMAD  
**هاتف:** +963998171954

## ✨ الميزات الرئيسية

### 📥 واجهة إدخال بيانات المواد
- إدخال شامل لجميع بيانات المواد
- تواريخ بداية ونهاية التشغيل
- تصنيف المواد حسب النوع واللون والموديل
- تتبع الأمتار المستخدمة والعدد المنتج والمسلم
- إدارة حالة المواد (مستخدمة، متوقفة، تحت الفحص)
- حقل ملاحظات للتفاصيل الإضافية

### 🔍 نظام البحث والاستعلام الذكي
- بحث متقدم حسب نوع المادة، اللون، الموديل
- فلترة حسب الفترة الزمنية وحالة المادة
- عرض إحصائيات مفصلة لنتائج البحث
- واجهة سهلة الاستخدام مع نتائج فورية

### 📊 لوحة التحكم والإحصائيات
- عرض الإحصائيات الرئيسية في الوقت الفعلي
- إجمالي المواد والأمتار المستخدمة
- إجمالي العدد المنتج والمسلم
- عرض آخر المواد المضافة
- مؤشرات أداء بصرية

### 📄 نظام التقارير الاحترافي
#### 1. تقرير استهلاك مادة حسب التاريخ
- تحديد نوع المادة والفترة الزمنية
- تفاصيل الاستخدام خلال الفترة المحددة
- مؤشرات استهلاك يومية

#### 2. التقرير الشامل لمادة معينة
- جميع السجلات الخاصة بمادة محددة
- معدل الاستهلاك العام
- مقارنة بين الإنتاج والتسليم

#### 3. التقرير اليومي
- عرض المواد المستهلكة في يوم محدد
- إمكانية فلترة حسب نوع المادة
- بيانات مناسبة للرقابة والتحليل

### 📋 إدارة قائمة المواد
- عرض جميع المواد في جدول تفاعلي
- بحث سريع في جميع البيانات
- ترتيب حسب التاريخ، النوع، أو الأمتار
- عرض تفاصيل كاملة لكل مادة
- إمكانية حذف المواد غير المرغوبة

### 💾 نظام التخزين والنسخ الاحتياطي
- حفظ تلقائي للبيانات محلياً
- تصدير البيانات بصيغة JSON
- استيراد البيانات من ملفات النسخ الاحتياطي
- تصدير التقارير بصيغة CSV/Excel

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Bootstrap 5** - إطار العمل للتصميم المتجاوب
- **Bootstrap Icons** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

## 🚀 كيفية الاستخدام

### التشغيل
1. افتح ملف `index.html` في أي متصفح حديث
2. لا حاجة لخادم ويب - يعمل محلياً بالكامل

### إضافة مادة جديدة
1. انقر على "إضافة مادة" في شريط التنقل
2. املأ جميع الحقول المطلوبة
3. انقر "حفظ المادة"

### البحث عن المواد
1. انتقل إلى قسم "البحث والاستعلام"
2. استخدم الفلاتر المتاحة
3. انقر "بحث" لعرض النتائج

### إنشاء التقارير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. املأ المعايير المطلوبة
4. انقر "إنشاء التقرير"
5. يمكنك طباعة أو تحميل التقرير

### إدارة البيانات
- **التصدير:** انقر "تصدير البيانات" في الرأس
- **الاستيراد:** انقر "استيراد البيانات" واختر ملف JSON

## 📱 التوافق

- ✅ جميع المتصفحات الحديثة
- ✅ أجهزة سطح المكتب والهواتف الذكية
- ✅ دعم كامل للغة العربية (RTL)
- ✅ تصميم متجاوب لجميع أحجام الشاشات

## 🔒 الأمان والخصوصية

- جميع البيانات تُحفظ محلياً في المتصفح
- لا يتم إرسال أي بيانات لخوادم خارجية
- إمكانية النسخ الاحتياطي الكامل للبيانات

## 📞 الدعم والتواصل

**المطور:** MOHANNAD AHMAD  
**هاتف:** +963998171954  
**البريد الإلكتروني:** [يرجى إضافة البريد الإلكتروني]

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي:** يُنصح بعمل نسخة احتياطية دورية للبيانات
2. **المتصفح:** استخدم متصفحاً حديثاً للحصول على أفضل أداء
3. **البيانات:** تأكد من دقة البيانات المدخلة قبل الحفظ
4. **التقارير:** يمكن طباعة التقارير مباشرة أو حفظها كملفات

## 🔄 التحديثات المستقبلية

- [ ] إضافة رسوم بيانية للإحصائيات
- [ ] نظام تنبيهات للمواد منخفضة المخزون
- [ ] تصدير التقارير بصيغة PDF
- [ ] نظام المستخدمين والصلاحيات
- [ ] ربط مع قواعد بيانات خارجية

---

**حقوق الطبع والنشر © 2024 SAM - برنامج استهلاك المواد**  
**جميع الحقوق محفوظة للمطور MOHANNAD AHMAD**
