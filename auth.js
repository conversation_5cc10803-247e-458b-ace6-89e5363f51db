// SAM - Authentication and Session Management

class AuthManager {
    constructor(configManager) {
        this.configManager = configManager;
        this.currentUser = null;
        this.sessionTimer = null;
        this.init();
    }

    init() {
        this.loadSession();
        this.setupSessionTimeout();
    }

    loadSession() {
        const stored = localStorage.getItem('sam_current_user');
        if (stored) {
            const sessionData = JSON.parse(stored);
            const sessionAge = Date.now() - sessionData.timestamp;
            const maxAge = this.configManager.config.sessionTimeout * 60 * 1000;

            if (sessionAge < maxAge) {
                this.currentUser = sessionData.user;
                this.updateSessionTimestamp();
                return true;
            } else {
                this.logout();
            }
        }
        return false;
    }

    saveSession(user) {
        const sessionData = {
            user: user,
            timestamp: Date.now()
        };
        localStorage.setItem('sam_current_user', JSON.stringify(sessionData));
        this.currentUser = user;
    }

    updateSessionTimestamp() {
        if (this.currentUser) {
            this.saveSession(this.currentUser);
        }
    }

    setupSessionTimeout() {
        // Clear existing timer
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
        }

        // Set up new timer to check session every minute
        this.sessionTimer = setInterval(() => {
            if (this.currentUser) {
                const stored = localStorage.getItem('sam_current_user');
                if (stored) {
                    const sessionData = JSON.parse(stored);
                    const sessionAge = Date.now() - sessionData.timestamp;
                    const maxAge = this.configManager.config.sessionTimeout * 60 * 1000;

                    if (sessionAge >= maxAge) {
                        this.logout();
                        this.showSessionExpiredMessage();
                    }
                }
            }
        }, 60000); // Check every minute
    }

    login(username, password) {
        const result = this.configManager.validateLogin(username, password);
        
        if (result.success) {
            this.saveSession(result.user);
            this.setupSessionTimeout();
            return { success: true, user: result.user };
        }
        
        return { success: false, message: result.message };
    }

    logout() {
        localStorage.removeItem('sam_current_user');
        this.currentUser = null;
        
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
            this.sessionTimer = null;
        }
        
        // Redirect to login page
        this.showLoginForm();
    }

    isLoggedIn() {
        return this.currentUser !== null;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.configManager.hasPermission(this.currentUser.role, permission);
    }

    requirePermission(permission) {
        if (!this.hasPermission(permission)) {
            this.showAccessDeniedMessage();
            return false;
        }
        return true;
    }

    showLoginForm() {
        // Hide main content
        const mainContent = document.querySelector('main');
        const navbar = document.querySelector('nav');
        const header = document.querySelector('header');
        
        if (mainContent) mainContent.style.display = 'none';
        if (navbar) navbar.style.display = 'none';
        if (header) header.style.display = 'none';

        // Show login form
        this.createLoginForm();
    }

    hideLoginForm() {
        // Show main content
        const mainContent = document.querySelector('main');
        const navbar = document.querySelector('nav');
        const header = document.querySelector('header');
        
        if (mainContent) mainContent.style.display = 'block';
        if (navbar) navbar.style.display = 'block';
        if (header) header.style.display = 'block';

        // Remove login form
        const loginContainer = document.getElementById('loginContainer');
        if (loginContainer) {
            loginContainer.remove();
        }
    }

    createLoginForm() {
        // Remove existing login form
        const existingForm = document.getElementById('loginContainer');
        if (existingForm) {
            existingForm.remove();
        }

        const loginHTML = `
            <div id="loginContainer" class="login-container">
                <div class="login-card">
                    <div class="login-header">
                        <div class="logo">
                            <i class="bi bi-clipboard-data"></i>
                        </div>
                        <h2>SAM - برنامج استهلاك المواد</h2>
                        <p class="text-muted">تسجيل الدخول للنظام</p>
                    </div>
                    
                    <form id="loginForm" class="login-form">
                        <div class="form-group">
                            <label for="username">
                                <i class="bi bi-person"></i>
                                اسم المستخدم
                            </label>
                            <input type="text" id="username" class="form-control" required autocomplete="username">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">
                                <i class="bi bi-lock"></i>
                                كلمة المرور
                            </label>
                            <div class="password-input">
                                <input type="password" id="password" class="form-control" required autocomplete="current-password">
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" id="rememberMe" class="form-check-input">
                                <label for="rememberMe" class="form-check-label">
                                    تذكرني
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="bi bi-box-arrow-in-right"></i>
                            تسجيل الدخول
                        </button>
                        
                        <div id="loginError" class="alert alert-danger d-none"></div>
                    </form>
                    
                    <div class="login-footer">
                        <div class="demo-accounts">
                            <h6>حسابات تجريبية:</h6>
                            <div class="demo-account" data-username="admin" data-password="admin123">
                                <strong>مدير:</strong> admin / admin123
                            </div>
                            <div class="demo-account" data-username="manager" data-password="manager123">
                                <strong>مدير عمليات:</strong> manager / manager123
                            </div>
                            <div class="demo-account" data-username="user" data-password="user123">
                                <strong>مستخدم:</strong> user / user123
                            </div>
                        </div>
                        
                        <div class="developer-info">
                            <p class="mb-0">المطور: MOHANNAD AHMAD</p>
                            <p class="mb-0">هاتف: +************</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', loginHTML);
        this.setupLoginFormEvents();
    }

    setupLoginFormEvents() {
        const loginForm = document.getElementById('loginForm');
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const demoAccounts = document.querySelectorAll('.demo-account');

        // Login form submission
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Password toggle
        togglePassword.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            togglePassword.querySelector('i').className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
        });

        // Demo account clicks
        demoAccounts.forEach(account => {
            account.addEventListener('click', () => {
                const username = account.getAttribute('data-username');
                const password = account.getAttribute('data-password');
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
            });
        });
    }

    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');

        const result = this.login(username, password);

        if (result.success) {
            errorDiv.classList.add('d-none');
            this.hideLoginForm();
            this.updateUIForUser();
            this.showWelcomeMessage(result.user);
        } else {
            errorDiv.textContent = result.message;
            errorDiv.classList.remove('d-none');
        }
    }

    updateUIForUser() {
        if (!this.currentUser) return;

        // Update header with user info
        this.updateHeaderUserInfo();
        
        // Update navigation based on permissions
        this.updateNavigationPermissions();
    }

    updateHeaderUserInfo() {
        const header = document.querySelector('header .container .row .col-md-8');
        if (header) {
            const userInfo = `
                <div class="user-info">
                    <small class="text-light d-block">
                        مرحباً، ${this.currentUser.fullName} (${this.currentUser.permissions.name})
                    </small>
                </div>
            `;
            header.insertAdjacentHTML('beforeend', userInfo);
        }

        // Add logout button
        const headerActions = document.querySelector('header .container .row .col-md-4');
        if (headerActions) {
            const logoutBtn = `
                <button class="btn btn-outline-light btn-sm ms-2" id="logoutBtn">
                    <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                </button>
            `;
            headerActions.insertAdjacentHTML('beforeend', logoutBtn);
            
            document.getElementById('logoutBtn').addEventListener('click', () => {
                this.logout();
            });
        }
    }

    updateNavigationPermissions() {
        const navLinks = document.querySelectorAll('.nav-link[data-section]');
        
        navLinks.forEach(link => {
            const section = link.getAttribute('data-section');
            let hasAccess = true;

            switch (section) {
                case 'add-material':
                    hasAccess = this.hasPermission('canAddMaterials');
                    break;
                case 'reports':
                    hasAccess = this.hasPermission('canViewReports');
                    break;
                case 'materials-list':
                    hasAccess = true; // Everyone can view list
                    break;
            }

            if (!hasAccess) {
                link.style.display = 'none';
            }
        });

        // Update export/import buttons
        const exportBtn = document.getElementById('exportDataBtn');
        const importBtn = document.getElementById('importDataBtn');
        
        if (exportBtn && !this.hasPermission('canExportData')) {
            exportBtn.style.display = 'none';
        }
        
        if (importBtn && !this.hasPermission('canImportData')) {
            importBtn.style.display = 'none';
        }
    }

    showWelcomeMessage(user) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-check-circle"></i>
            مرحباً ${user.fullName}! تم تسجيل الدخول بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alert, main.firstChild);
            setTimeout(() => alert.remove(), 5000);
        }
    }

    showSessionExpiredMessage() {
        const alert = document.createElement('div');
        alert.className = 'alert alert-warning alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-exclamation-triangle"></i>
            انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.insertBefore(alert, document.body.firstChild);
        setTimeout(() => alert.remove(), 5000);
    }

    showAccessDeniedMessage() {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-shield-exclamation"></i>
            ليس لديك صلاحية للوصول إلى هذه الميزة
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alert, main.firstChild);
            setTimeout(() => alert.remove(), 5000);
        }
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.AuthManager = AuthManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
