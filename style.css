/* SAM - Material Consumption Management System Styles */

/* Arabic Font and RTL Support */
* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

header h1 {
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: #e9ecef;
    color: #007bff !important;
}

.navbar-nav .nav-link.active {
    background-color: #007bff;
    color: white !important;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62 0%, #3d4142 100%);
    transform: translateY(-1px);
}

/* Table Styles */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #007bff;
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

/* Content Sections */
.content-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .h3 {
        font-size: 1.5rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem;
        margin: 0.1rem;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    margin-top: auto;
}

/* Custom Utilities */
.text-shadow {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.border-radius-lg {
    border-radius: 1rem !important;
}

.shadow-lg-custom {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Print Styles */
@media print {
    .navbar,
    footer,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}

/* Additional Enhancements */
.report-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 1rem 1rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Statistics Animation */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    transition: all 0.3s ease;
}

.card.bg-primary:hover,
.card.bg-success:hover,
.card.bg-info:hover,
.card.bg-warning:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Badge Enhancements */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 0.5rem;
}

/* Search Results Highlighting */
.search-highlight {
    background-color: yellow;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #121212;
        color: #ffffff;
    }

    .card {
        background-color: #1e1e1e;
        color: #ffffff;
    }

    .form-control,
    .form-select {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }

    .table {
        color: #ffffff;
    }

    .table tbody tr:hover {
        background-color: #2d2d2d;
    }

    .modal-content {
        background-color: #1e1e1e;
        color: #ffffff;
    }
}
