// SAM - Material Consumption Management System
// Configuration and User Management

class ConfigManager {
    constructor() {
        this.defaultConfig = {
            appName: "SAM - برنامج استهلاك المواد",
            version: "1.0.0",
            developer: "MOHANNAD AHMAD",
            phone: "+963998171954",
            theme: "light", // light or dark
            language: "ar",
            autoSave: true,
            sessionTimeout: 30, // minutes
            maxLoginAttempts: 3,
            lockoutDuration: 15, // minutes
            permissions: {
                admin: {
                    name: "مدير النظام",
                    canAddMaterials: true,
                    canEditMaterials: true,
                    canDeleteMaterials: true,
                    canViewReports: true,
                    canExportData: true,
                    canImportData: true,
                    canManageUsers: true,
                    canChangeSettings: true
                },
                manager: {
                    name: "مدير",
                    canAddMaterials: true,
                    canEditMaterials: true,
                    canDeleteMaterials: false,
                    canViewReports: true,
                    canExportData: true,
                    canImportData: false,
                    canManageUsers: false,
                    canChangeSettings: false
                },
                user: {
                    name: "مستخدم",
                    canAddMaterials: true,
                    canEditMaterials: false,
                    canDeleteMaterials: false,
                    canViewReports: true,
                    canExportData: false,
                    canImportData: false,
                    canManageUsers: false,
                    canChangeSettings: false
                },
                viewer: {
                    name: "مشاهد",
                    canAddMaterials: false,
                    canEditMaterials: false,
                    canDeleteMaterials: false,
                    canViewReports: true,
                    canExportData: false,
                    canImportData: false,
                    canManageUsers: false,
                    canChangeSettings: false
                }
            }
        };

        this.defaultUsers = [
            {
                id: "admin001",
                username: "admin",
                password: this.hashPassword("admin123"),
                fullName: "مدير النظام",
                email: "<EMAIL>",
                role: "admin",
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lockedUntil: null
            },
            {
                id: "manager001",
                username: "manager",
                password: this.hashPassword("manager123"),
                fullName: "مدير العمليات",
                email: "<EMAIL>",
                role: "manager",
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lockedUntil: null
            },
            {
                id: "user001",
                username: "user",
                password: this.hashPassword("user123"),
                fullName: "مستخدم عادي",
                email: "<EMAIL>",
                role: "user",
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lockedUntil: null
            }
        ];

        this.init();
    }

    init() {
        this.loadConfig();
        this.loadUsers();
    }

    // Simple hash function for passwords (in production, use proper hashing)
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    loadConfig() {
        const stored = localStorage.getItem('sam_config');
        this.config = stored ? { ...this.defaultConfig, ...JSON.parse(stored) } : this.defaultConfig;
    }

    saveConfig() {
        localStorage.setItem('sam_config', JSON.stringify(this.config));
    }

    loadUsers() {
        const stored = localStorage.getItem('sam_users');
        this.users = stored ? JSON.parse(stored) : this.defaultUsers;
        
        // If no users exist, create default users
        if (this.users.length === 0) {
            this.users = this.defaultUsers;
            this.saveUsers();
        }
    }

    saveUsers() {
        localStorage.setItem('sam_users', JSON.stringify(this.users));
    }

    getConfig() {
        return this.config;
    }

    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveConfig();
    }

    getUsers() {
        return this.users;
    }

    getUserById(id) {
        return this.users.find(user => user.id === id);
    }

    getUserByUsername(username) {
        return this.users.find(user => user.username === username);
    }

    addUser(userData) {
        const newUser = {
            id: 'user_' + Date.now(),
            ...userData,
            password: this.hashPassword(userData.password),
            createdAt: new Date().toISOString(),
            lastLogin: null,
            loginAttempts: 0,
            lockedUntil: null
        };
        
        this.users.push(newUser);
        this.saveUsers();
        return newUser;
    }

    updateUser(userId, userData) {
        const userIndex = this.users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
            if (userData.password) {
                userData.password = this.hashPassword(userData.password);
            }
            this.users[userIndex] = { ...this.users[userIndex], ...userData };
            this.saveUsers();
            return this.users[userIndex];
        }
        return null;
    }

    deleteUser(userId) {
        const userIndex = this.users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
            this.users.splice(userIndex, 1);
            this.saveUsers();
            return true;
        }
        return false;
    }

    validateLogin(username, password) {
        const user = this.getUserByUsername(username);
        
        if (!user) {
            return { success: false, message: 'اسم المستخدم غير موجود' };
        }

        if (!user.isActive) {
            return { success: false, message: 'الحساب غير مفعل' };
        }

        // Check if account is locked
        if (user.lockedUntil && new Date() < new Date(user.lockedUntil)) {
            const remainingTime = Math.ceil((new Date(user.lockedUntil) - new Date()) / (1000 * 60));
            return { success: false, message: `الحساب مقفل لمدة ${remainingTime} دقيقة` };
        }

        // Check password
        const hashedPassword = this.hashPassword(password);
        if (user.password !== hashedPassword) {
            // Increment login attempts
            user.loginAttempts = (user.loginAttempts || 0) + 1;
            
            if (user.loginAttempts >= this.config.maxLoginAttempts) {
                user.lockedUntil = new Date(Date.now() + this.config.lockoutDuration * 60 * 1000).toISOString();
                this.saveUsers();
                return { success: false, message: `تم قفل الحساب لمدة ${this.config.lockoutDuration} دقيقة بسبب المحاولات الخاطئة` };
            }
            
            this.saveUsers();
            return { success: false, message: `كلمة المرور خاطئة. المحاولات المتبقية: ${this.config.maxLoginAttempts - user.loginAttempts}` };
        }

        // Successful login
        user.lastLogin = new Date().toISOString();
        user.loginAttempts = 0;
        user.lockedUntil = null;
        this.saveUsers();

        return { 
            success: true, 
            user: {
                id: user.id,
                username: user.username,
                fullName: user.fullName,
                email: user.email,
                role: user.role,
                permissions: this.config.permissions[user.role]
            }
        };
    }

    hasPermission(userRole, permission) {
        const rolePermissions = this.config.permissions[userRole];
        return rolePermissions && rolePermissions[permission];
    }

    getPermissions(userRole) {
        return this.config.permissions[userRole] || {};
    }

    // Reset all data (for testing purposes)
    resetToDefaults() {
        localStorage.removeItem('sam_config');
        localStorage.removeItem('sam_users');
        localStorage.removeItem('sam_materials');
        localStorage.removeItem('sam_current_user');
        this.init();
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.ConfigManager = ConfigManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfigManager;
}
