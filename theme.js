// SAM - Theme Management System

class ThemeManager {
    constructor(configManager) {
        this.configManager = configManager;
        this.currentTheme = 'light';
        this.init();
    }

    init() {
        this.loadTheme();
        this.createThemeToggle();
        this.applyTheme();
    }

    loadTheme() {
        // Load from config
        const config = this.configManager.getConfig();
        this.currentTheme = config.theme || 'light';
        
        // Check for system preference if not set
        if (!config.theme) {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.currentTheme = prefersDark ? 'dark' : 'light';
        }
    }

    saveTheme() {
        this.configManager.updateConfig({ theme: this.currentTheme });
    }

    applyTheme() {
        const body = document.body;
        
        if (this.currentTheme === 'dark') {
            body.classList.add('dark-theme');
            body.classList.remove('light-theme');
        } else {
            body.classList.add('light-theme');
            body.classList.remove('dark-theme');
        }

        // Update theme toggle icon
        this.updateThemeToggleIcon();
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor();
        
        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', { 
            detail: { theme: this.currentTheme } 
        }));
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        this.saveTheme();
        this.showThemeChangeNotification();
    }

    setTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.currentTheme = theme;
            this.applyTheme();
            this.saveTheme();
        }
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    createThemeToggle() {
        // Remove existing toggle if any
        const existingToggle = document.getElementById('themeToggle');
        if (existingToggle) {
            existingToggle.remove();
        }

        const themeToggle = document.createElement('button');
        themeToggle.id = 'themeToggle';
        themeToggle.className = 'theme-toggle';
        themeToggle.title = 'تغيير الثيم';
        themeToggle.innerHTML = '<i class="bi bi-sun"></i>';
        
        themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });

        document.body.appendChild(themeToggle);
    }

    updateThemeToggleIcon() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = this.currentTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
            }
        }
    }

    updateMetaThemeColor() {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }

        const color = this.currentTheme === 'dark' ? '#121212' : '#007bff';
        metaThemeColor.content = color;
    }

    showThemeChangeNotification() {
        const notification = document.createElement('div');
        notification.className = 'theme-notification';
        notification.innerHTML = `
            <i class="bi bi-${this.currentTheme === 'dark' ? 'moon' : 'sun'}"></i>
            تم التبديل إلى الثيم ${this.currentTheme === 'dark' ? 'المظلم' : 'المضيء'}
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Auto theme switching based on time
    enableAutoTheme() {
        const now = new Date();
        const hour = now.getHours();
        
        // Switch to dark theme between 6 PM and 6 AM
        const shouldBeDark = hour >= 18 || hour < 6;
        const newTheme = shouldBeDark ? 'dark' : 'light';
        
        if (newTheme !== this.currentTheme) {
            this.setTheme(newTheme);
        }
    }

    // Listen for system theme changes
    listenForSystemThemeChanges() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', (e) => {
            const newTheme = e.matches ? 'dark' : 'light';
            this.setTheme(newTheme);
        });
    }

    // Get theme-specific colors
    getThemeColors() {
        const themes = {
            light: {
                primary: '#007bff',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                background: '#ffffff',
                surface: '#f8f9fa',
                text: '#212529',
                textMuted: '#6c757d'
            },
            dark: {
                primary: '#0d6efd',
                secondary: '#6c757d',
                success: '#198754',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#0dcaf0',
                background: '#121212',
                surface: '#1e1e1e',
                text: '#ffffff',
                textMuted: '#adb5bd'
            }
        };

        return themes[this.currentTheme];
    }

    // Apply theme to specific elements
    applyThemeToElement(element, styles) {
        const colors = this.getThemeColors();
        
        Object.keys(styles).forEach(property => {
            const value = styles[property];
            if (typeof value === 'string' && value.startsWith('theme.')) {
                const colorKey = value.replace('theme.', '');
                element.style[property] = colors[colorKey];
            } else {
                element.style[property] = value;
            }
        });
    }

    // Create theme settings panel
    createThemeSettings() {
        const settingsHTML = `
            <div class="theme-settings">
                <h6>إعدادات الثيم</h6>
                <div class="theme-options">
                    <div class="theme-option" data-theme="light">
                        <div class="theme-preview light-preview">
                            <div class="preview-header"></div>
                            <div class="preview-content"></div>
                        </div>
                        <span>مضيء</span>
                    </div>
                    <div class="theme-option" data-theme="dark">
                        <div class="theme-preview dark-preview">
                            <div class="preview-header"></div>
                            <div class="preview-content"></div>
                        </div>
                        <span>مظلم</span>
                    </div>
                    <div class="theme-option" data-theme="auto">
                        <div class="theme-preview auto-preview">
                            <div class="preview-header"></div>
                            <div class="preview-content"></div>
                        </div>
                        <span>تلقائي</span>
                    </div>
                </div>
                <div class="theme-actions">
                    <button class="btn btn-sm btn-outline-primary" id="enableAutoTheme">
                        <i class="bi bi-clock"></i> تفعيل التبديل التلقائي
                    </button>
                </div>
            </div>
        `;

        return settingsHTML;
    }
}

// Add CSS for theme notification
const themeNotificationCSS = `
    .theme-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .theme-notification.show {
        transform: translateX(0);
    }

    .theme-notification i {
        margin-left: 8px;
    }

    .theme-settings {
        padding: 1rem;
    }

    .theme-options {
        display: flex;
        gap: 1rem;
        margin: 1rem 0;
    }

    .theme-option {
        text-align: center;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .theme-option:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }

    .theme-option.active {
        background-color: rgba(0, 123, 255, 0.2);
    }

    .theme-preview {
        width: 60px;
        height: 40px;
        border-radius: 4px;
        margin-bottom: 0.5rem;
        overflow: hidden;
        border: 2px solid #dee2e6;
    }

    .light-preview {
        background: #ffffff;
    }

    .light-preview .preview-header {
        background: #007bff;
        height: 12px;
    }

    .light-preview .preview-content {
        background: #f8f9fa;
        height: 26px;
    }

    .dark-preview {
        background: #121212;
    }

    .dark-preview .preview-header {
        background: #0d6efd;
        height: 12px;
    }

    .dark-preview .preview-content {
        background: #1e1e1e;
        height: 26px;
    }

    .auto-preview {
        background: linear-gradient(45deg, #ffffff 50%, #121212 50%);
    }

    .auto-preview .preview-header {
        background: linear-gradient(45deg, #007bff 50%, #0d6efd 50%);
        height: 12px;
    }

    .auto-preview .preview-content {
        background: linear-gradient(45deg, #f8f9fa 50%, #1e1e1e 50%);
        height: 26px;
    }
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = themeNotificationCSS;
document.head.appendChild(style);

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.ThemeManager = ThemeManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
